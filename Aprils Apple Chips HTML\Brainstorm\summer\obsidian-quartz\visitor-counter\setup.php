<?php
/**
 * Visitor Counter Setup Script
 * For A. A. Chips' Obsidian-Quartz Blog
 * 
 * This script sets up the visitor counter database tables and tests functionality.
 * Run this once to initialize the visitor counter system.
 */

// Prevent direct access from web
if (php_sapi_name() !== 'cli' && !isset($_GET['setup_key']) || $_GET['setup_key'] !== 'visitor_counter_setup_2025') {
    die('Access denied. Run from command line or use setup_key parameter.');
}

echo "=== Visitor Counter Setup ===\n";
echo "Setting up visitor counter for A. A. Chips' Obsidian-Quartz Blog\n\n";

try {
    // Include the comments database connection
    require_once __DIR__ . '/../comments/database.php';
    
    echo "1. Connecting to database...\n";
    $db = CommentDatabase::getInstance();
    $pdo = $db->getPDO();
    echo "   ✓ Database connection successful\n\n";
    
    echo "2. Reading SQL schema...\n";
    $sqlFile = __DIR__ . '/visitor_counter.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    echo "   ✓ SQL schema loaded\n\n";
    
    echo "3. Creating database tables...\n";
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            
            // Extract table name for feedback
            if (preg_match('/CREATE TABLE.*?`?(\w+)`?\s*\(/i', $statement, $matches)) {
                echo "   ✓ Created table: {$matches[1]}\n";
            } elseif (preg_match('/CREATE.*?PROCEDURE\s+(\w+)/i', $statement, $matches)) {
                echo "   ✓ Created procedure: {$matches[1]}\n";
            } elseif (preg_match('/CREATE.*?VIEW\s+(\w+)/i', $statement, $matches)) {
                echo "   ✓ Created view: {$matches[1]}\n";
            } elseif (preg_match('/INSERT.*?INTO\s+(\w+)/i', $statement, $matches)) {
                echo "   ✓ Inserted data into: {$matches[1]}\n";
            }
        } catch (PDOException $e) {
            // Ignore "already exists" errors
            if (strpos($e->getMessage(), 'already exists') === false) {
                echo "   ⚠ Warning: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n4. Testing visitor counter functionality...\n";
    
    // Include visitor counter class
    require_once __DIR__ . '/VisitorCounter.php';
    $counter = new VisitorCounter($db);
    
    // Test recording a visit
    echo "   Testing visit recording...\n";
    $testPageSlug = 'test-page-' . date('Y-m-d-H-i-s');
    $testPageTitle = 'Test Page for Visitor Counter';
    
    $result = $counter->recordVisit($testPageSlug, $testPageTitle);
    if ($result) {
        echo "   ✓ Visit recorded successfully\n";
    } else {
        echo "   ⚠ Visit recording failed or was duplicate\n";
    }
    
    // Test getting page stats
    echo "   Testing page statistics retrieval...\n";
    $stats = $counter->getPageStats($testPageSlug);
    if ($stats && $stats['total_visits'] > 0) {
        echo "   ✓ Page stats retrieved: {$stats['total_visits']} visits\n";
    } else {
        echo "   ⚠ Page stats retrieval failed\n";
    }
    
    // Test getting site stats
    echo "   Testing site statistics retrieval...\n";
    $siteStats = $counter->getSiteStats();
    if (!empty($siteStats)) {
        echo "   ✓ Site stats retrieved: " . ($siteStats['total_site_visits'] ?? 0) . " total visits\n";
    } else {
        echo "   ⚠ Site stats retrieval failed\n";
    }
    
    // Test display functionality
    echo "   Testing display functionality...\n";
    require_once __DIR__ . '/visitor-display.php';
    
    $displayHtml = displayVisitorCounter($testPageSlug, $testPageTitle, [
        'track_visit' => false, // Don't record another visit
        'style' => 'retro'
    ]);
    
    if (!empty($displayHtml) && strpos($displayHtml, 'visitor-counter') !== false) {
        echo "   ✓ Display functionality working\n";
    } else {
        echo "   ⚠ Display functionality failed\n";
    }
    
    // Test retro counter display
    echo "   Testing retro counter display...\n";
    $retroHtml = displayRetroCounter($testPageSlug, [
        'digits' => 6,
        'style' => 'lcd'
    ]);
    
    if (!empty($retroHtml) && strpos($retroHtml, 'retro-counter') !== false) {
        echo "   ✓ Retro counter display working\n";
    } else {
        echo "   ⚠ Retro counter display failed\n";
    }
    
    echo "\n5. Verifying database structure...\n";
    
    // Check if all required tables exist
    $requiredTables = [
        'aachipsc_blog_page_visits',
        'aachipsc_blog_page_stats',
        'aachipsc_blog_site_stats',
        'aachipsc_blog_visitor_sessions',
        'aachipsc_blog_daily_stats'
    ];
    
    foreach ($requiredTables as $table) {
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            echo "   ✓ Table exists: $table\n";
        } else {
            echo "   ✗ Table missing: $table\n";
        }
    }
    
    // Check if stored procedures exist
    $stmt = $pdo->prepare("SHOW PROCEDURE STATUS WHERE Name = 'UpdatePageStats'");
    $stmt->execute();
    if ($stmt->fetch()) {
        echo "   ✓ Stored procedure exists: UpdatePageStats\n";
    } else {
        echo "   ✗ Stored procedure missing: UpdatePageStats\n";
    }
    
    echo "\n6. Setup complete!\n\n";
    
    echo "=== Next Steps ===\n";
    echo "1. The visitor counter is now installed and ready to use\n";
    echo "2. Visit any page on your site to test the counter\n";
    echo "3. Check the footer of your pages for the visitor counter display\n";
    echo "4. The counter will automatically track unique daily visits\n";
    echo "5. You can customize the display by editing visitor-display.php\n\n";
    
    echo "=== Configuration Options ===\n";
    echo "- Edit footer.php to change counter display options\n";
    echo "- Modify visitor-counter.css to customize styling\n";
    echo "- Use different display formats: 'retro', 'modern', 'minimal'\n";
    echo "- Enable/disable today's visit counts in display options\n\n";
    
    echo "=== Maintenance ===\n";
    echo "- Old visit data can be cleaned using: CALL CleanOldVisitData(365)\n";
    echo "- Statistics are updated automatically on each visit\n";
    echo "- Check logs for any visitor counter errors\n\n";
    
    // Clean up test data
    echo "Cleaning up test data...\n";
    $stmt = $pdo->prepare("DELETE FROM aachipsc_blog_page_visits WHERE page_slug = ?");
    $stmt->execute([$testPageSlug]);
    $stmt = $pdo->prepare("DELETE FROM aachipsc_blog_page_stats WHERE page_slug = ?");
    $stmt->execute([$testPageSlug]);
    echo "✓ Test data cleaned up\n\n";
    
    echo "Setup completed successfully! 🎉\n";
    
} catch (Exception $e) {
    echo "\n❌ Setup failed: " . $e->getMessage() . "\n";
    echo "Please check your database configuration and try again.\n";
    exit(1);
}

/**
 * Display usage information
 */
function showUsage() {
    echo "Usage:\n";
    echo "  Command line: php setup.php\n";
    echo "  Web browser: setup.php?setup_key=visitor_counter_setup_2025\n\n";
    echo "This script will:\n";
    echo "  - Create visitor counter database tables\n";
    echo "  - Set up stored procedures and views\n";
    echo "  - Test all functionality\n";
    echo "  - Verify the installation\n\n";
}

// Show usage if requested
if (isset($argv[1]) && ($argv[1] === '--help' || $argv[1] === '-h')) {
    showUsage();
    exit(0);
}
